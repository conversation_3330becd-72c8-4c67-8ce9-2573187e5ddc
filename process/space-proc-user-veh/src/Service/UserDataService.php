<?php

namespace App\Service;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Document\Vehicle;
use Space\MongoDocuments\Service\MongoDBService;
use Symfony\Component\HttpFoundation\Response;

/**
 * UserDataService handles user data operations using MongoDB ODM
 * Following the established pattern from space-proc-me microservice
 */
class UserDataService
{
    use LoggerTrait;

    public function __construct(
        private MongoDBService $mongoDBService
    ) {
    }

    /**
     * Find user by ID
     */
    public function findUserById(string $userId): ?UserData
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Finding user by ID', [
                'userId' => $userId,
            ]);

            return $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error finding user by ID', [
                'userId' => $userId,
                'error' => $e->getMessage(),
                'error_type' => get_class($e),
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                throw new \RuntimeException('MongoDB connection unavailable', 503, $e);
            }

            return null;
        }
    }

    /**
     * Check if exception is a MongoDB connection error
     */
    private function isMongoConnectionError(\Exception $e): bool
    {
        return str_contains($e->getMessage(), 'Connection') ||
               str_contains($e->getMessage(), 'connection') ||
               str_contains($e->getMessage(), 'timeout') ||
               str_contains($e->getMessage(), 'MongoDB');
    }



    /**
     * Update vehicle properties from source to target
     */
    private function updateVehicleProperties(Vehicle $target, Vehicle $source): void
    {
        if ($source->getBrand()) {
            $target->setBrand($source->getBrand());
        }
        if ($source->getModel()) {
            $target->setModel($source->getModel());
        }
        if ($source->getVersionId()) {
            $target->setVersionId($source->getVersionId());
        }
        if ($source->getFeatureCode()) {
            $target->setFeatureCode($source->getFeatureCode());
        }
        // Add more property updates as needed
    }

    /**
     * Save user data
     */
    public function saveUserData(UserData $userData): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Saving user data', [
                'userId' => $userData->getUserId(),
            ]);

            $this->mongoDBService->save($userData);

            return new SuccessResponse('User data saved successfully', Response::HTTP_OK);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error saving user data', [
                'userId' => $userData->getUserId(),
                'error' => $e->getMessage(),
                'error_type' => get_class($e),
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                return new ErrorResponse('Database service temporarily unavailable', Response::HTTP_SERVICE_UNAVAILABLE);
            }

            return new ErrorResponse('Error saving user data', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get user data document
     */
    public function getUserDataDocument(string $userId): ?UserData
    {
        return $this->findUserById($userId);
    }

    /**
     * Save user data document (backward compatibility method)
     */
    public function saveUserDataDocument(UserData $userData): ResponseArrayFormat
    {
        return $this->saveUserData($userData);
    }

    /**
     * Create a new UserData document
     */
    public function createUserData(string $userId): UserData
    {
        $userData = new UserData();
        $userData->setUserId($userId);
        return $userData;
    }

    /**
     * Convert legacy Vehicle to ODM Vehicle document
     */
    public function convertLegacyVehicleToODM($legacyVehicle): Vehicle
    {
        $vehicle = new Vehicle();

        // Handle both object and array formats
        if (is_object($legacyVehicle)) {
            $vehicle->setVin($legacyVehicle->vin ?? null);
            $vehicle->setBrand($legacyVehicle->brand ?? null);
            $vehicle->setModel($legacyVehicle->shortLabel ?? $legacyVehicle->modelDescription ?? null);
            $vehicle->setVersionId($legacyVehicle->versionId ?? null);
            $vehicle->setRegistrationNumber($legacyVehicle->registrationNumber ?? null);
            $vehicle->setColor($legacyVehicle->color ?? null);
            $vehicle->setEnergy($legacyVehicle->energy ?? null);

            // Set feature codes if available
            if (isset($legacyVehicle->featureCode) && is_array($legacyVehicle->featureCode)) {
                $vehicle->setFeatureCode($legacyVehicle->featureCode);
            }

            // Map SDP to status field
            if (isset($legacyVehicle->sdp)) {
                $vehicle->setStatus($legacyVehicle->sdp);
            }
        } elseif (is_array($legacyVehicle)) {
            $vehicle->setVin($legacyVehicle['vin'] ?? null);
            $vehicle->setBrand($legacyVehicle['brand'] ?? null);
            $vehicle->setModel($legacyVehicle['shortLabel'] ?? $legacyVehicle['modelDescription'] ?? null);
            $vehicle->setVersionId($legacyVehicle['versionId'] ?? null);
            $vehicle->setRegistrationNumber($legacyVehicle['registrationNumber'] ?? null);
            $vehicle->setColor($legacyVehicle['color'] ?? null);
            $vehicle->setEnergy($legacyVehicle['energy'] ?? null);

            // Set feature codes if available
            if (isset($legacyVehicle['featureCode']) && is_array($legacyVehicle['featureCode'])) {
                $vehicle->setFeatureCode($legacyVehicle['featureCode']);
            }

            // Map SDP to status field
            if (isset($legacyVehicle['sdp'])) {
                $vehicle->setStatus($legacyVehicle['sdp']);
            }
        }

        return $vehicle;
    }

    /**
     * Get PSA ID for user and brand
     */
    public function getPsaIdByUserAndBrand(string $userId, string $brand): ?string
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting PSA ID for user and brand', [
                'userId' => $userId,
                'brand' => $brand,
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                return null;
            }

            $userPsaIds = $userData->getUserPsaId();
            foreach ($userPsaIds as $psaId) {
                if ($psaId->getBrand() === $brand) {
                    return $psaId->getPsaId();
                }
            }

            return null;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting PSA ID for user and brand', [
                'userId' => $userId,
                'brand' => $brand,
                'exception' => $e->getMessage()
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                throw new \RuntimeException('MongoDB connection unavailable', 503, $e);
            }

            return null;
        }
    }
   
    /**
     * Fetch ACNT codes from user PSA IDs
     * @return array<array-key, string>
     */
    public function fetchACNTCodes(
        UserData $userData,
        array $toSkipBrands = []
    ): array
    {
        $list = [];
        $userPsaIds = $userData->getUserPsaId();

        if (empty($userPsaIds)) {
            return $list;
        }

        foreach ($userPsaIds as $psaId) {
            $brand = trim(strtoupper($psaId->getBrand()));
            if (!empty($toSkipBrands) && in_array($brand, $toSkipBrands)) {
                continue;
            }

            // Extract ACNT from PSA ID (assuming format like "ACNT-12345")
            $psaIdValue = $psaId->getPsaId();
            $acnt = $this->extractAcntFromPsaId($psaIdValue);
            if (empty($acnt)) {
                $this->logger->error('Invalid PSA ID format: ' . $psaIdValue, ['userId' => $userData->getUserId()]);
                continue;
            }

            $list[$brand] = $acnt;
        }
        return $list;
    }

    /**
     * Extract ACNT code from PSA ID
     */
    private function extractAcntFromPsaId(?string $psaId): ?string
    {
        if (empty($psaId)) {
            return null;
        }

        // Handle different PSA ID formats (e.g., "ACNT-12345" or "12345")
        if (str_contains($psaId, '-')) {
            $parts = explode('-', $psaId);
            return $parts[1] ?? null;
        }

        return $psaId;
    }

    /**
     * Remove user GSDP vehicles
     */
    public function removeUserGSPDVehicles(string $userId, string $brand, array $vinExcluded): bool
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Removing GSDP vehicles', [
                'userId' => $userId,
                'brand' => $brand,
                'vinExcluded' => $vinExcluded,
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                return false;
            }

            $vehicles = $userData->getVehicles();
            $vehiclesToRemove = [];

            foreach ($vehicles as $vehicle) {
                if ($vehicle->getBrand() === $brand &&
                    !in_array($vehicle->getVin(), $vinExcluded) &&
                    $vehicle->getStatus() === 'GSDP') {
                    $vehiclesToRemove[] = $vehicle;
                }
            }

            // Remove vehicles by creating a new filtered array
            $remainingVehicles = [];
            foreach ($vehicles as $vehicle) {
                if (!in_array($vehicle, $vehiclesToRemove, true)) {
                    $remainingVehicles[] = $vehicle;
                }
            }
            $userData->setVehicles($remainingVehicles);

            if (!empty($vehiclesToRemove)) {
                $this->mongoDBService->save($userData);
            }

            return true;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while removing GSDP vehicles', [
                'userId' => $userId,
                'brand' => $brand,
                'vinExcluded' => $vinExcluded,
                'exception' => $e->getMessage()
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                throw new \RuntimeException('MongoDB connection unavailable', 503, $e);
            }

            return false;
        }
    }

    /**
     * Remove user SSDP vehicles
     */
    public function removeUserSSDPVehicles(string $userId, string $vin): bool
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Removing SSDP vehicle', [
                'userId' => $userId,
                'vin' => $vin,
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                return false;
            }

            $vehicle = $userData->findVehicleByVin($vin);
            if (!$vehicle) {
                return false;
            }

            // For ODM compatibility: allow deletion of vehicles with SSDP status or no status
            $status = $vehicle->getStatus();
            if ($status !== null && $status !== 'SSDP') {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Vehicle status is not SSDP', [
                    'userId' => $userId,
                    'vin' => $vin,
                    'status' => $status
                ]);
                return false;
            }

            // Remove vehicle by creating a new filtered array
            $vehicles = $userData->getVehicles();
            $remainingVehicles = array_filter($vehicles, function($v) use ($vehicle) {
                return $v !== $vehicle;
            });
            $userData->setVehicles(array_values($remainingVehicles));
            $this->mongoDBService->save($userData);

            return true;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while removing SSDP vehicle', [
                'userId' => $userId,
                'vin' => $vin,
                'exception' => $e->getMessage()
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                throw new \RuntimeException('MongoDB connection unavailable', 503, $e);
            }

            return false;
        }
    }

    /**
     * Find user vehicle by VIN
     */
    public function findUserVehicleByVin(string $userId, string $vin): ?Vehicle
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Finding user vehicle by VIN', [
                'userId' => $userId,
                'vin' => $vin,
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                return null;
            }

            $vehicles = $userData->getVehicles();
            foreach ($vehicles as $vehicle) {
                if (strcasecmp($vehicle->getVin(), $vin) === 0) {
                    return $vehicle;
                }
            }

            return null;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while fetching user vehicle by vin', [
                'userId' => $userId,
                'vin' => $vin,
                'exception' => $e->getMessage()
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                throw new \RuntimeException('MongoDB connection unavailable', 503, $e);
            }

            throw $e;
        }
    }

    /**
     * Add vehicle to user document
     */
    public function addVehicleToUserDocument(string $userId, $vehicleData): bool
    {
        try {
            // Convert legacy vehicle to ODM Vehicle if needed
            if (!$vehicleData instanceof Vehicle) {
                $vehicle = $this->convertLegacyVehicleToODM($vehicleData);
            } else {
                $vehicle = $vehicleData;
            }

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Adding vehicle to user document', [
                'userId' => $userId,
                'vin' => $vehicle->getVin(),
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                // Create new user data if it doesn't exist
                $userData = new UserData();
                $userData->setUserId($userId);
            }

            $userData->addVehicle($vehicle);
            $this->mongoDBService->save($userData);

            return true;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while adding vehicle to user document', [
                'userId' => $userId,
                'exception' => $e->getMessage()
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                throw new \RuntimeException('MongoDB connection unavailable', 503, $e);
            }

            throw $e;
        }
    }

    /**
     * Update vehicle in user document
     */
    public function updateVehicleInUserDocument(string $userId, $vehicleData): bool
    {
        try {
            // Convert legacy vehicle to ODM Vehicle if needed
            if (!$vehicleData instanceof Vehicle) {
                $vehicle = $this->convertLegacyVehicleToODM($vehicleData);
            } else {
                $vehicle = $vehicleData;
            }

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updating vehicle in user document', [
                'userId' => $userId,
                'vin' => $vehicle->getVin(),
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' User not found', ['userId' => $userId]);
                return false;
            }

            // Find and update the vehicle
            $vin = $vehicle->getVin();
            $existingVehicle = $userData->findVehicleByVin($vin);

            if (!$existingVehicle) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Vehicle not found', [
                    'userId' => $userId,
                    'vin' => $vin
                ]);
                return false;
            }

            // Update vehicle properties
            $this->updateVehicleProperties($existingVehicle, $vehicle);

            $this->mongoDBService->save($userData);

            return true;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while updating vehicle in user document', [
                'userId' => $userId,
                'exception' => $e->getMessage()
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                throw new \RuntimeException('MongoDB connection unavailable', 503, $e);
            }

            throw $e;
        }
    }

    /**
     * Get a list of vehicles filtered by brand for a given userId.
     * @return Vehicle[]
     */
    public function getVehicleByUserIdAndBrand(string $userId, string $brand): array
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting vehicles by userId and brand', [
                'userId' => $userId,
                'brand' => $brand,
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                return [];
            }

            $vehicles = $userData->getVehicles();
            $filteredVehicles = [];

            foreach ($vehicles as $vehicle) {
                if (strcasecmp($vehicle->getBrand(), $brand) === 0) {
                    $filteredVehicles[] = $vehicle;
                }
            }

            return $filteredVehicles;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while fetching vehicles by userId and brand', [
                'userId' => $userId,
                'brand' => $brand,
                'exception' => $e->getMessage()
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                throw new \RuntimeException('MongoDB connection unavailable', 503, $e);
            }

            return [];
        }
    }

    /**
     * Delete all vehicles for a user
     */
    public function deleteVehiclesFieldForUser(string $userId): int
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Deleting all vehicles for user', [
                'userId' => $userId,
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                return 0;
            }

            $vehicleCount = count($userData->getVehicles());
            $userData->setVehicles([]);
            $this->mongoDBService->save($userData);

            return $vehicleCount;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while deleting vehicles for user', [
                'userId' => $userId,
                'exception' => $e->getMessage()
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                throw new \RuntimeException('MongoDB connection unavailable', 503, $e);
            }

            return 0;
        }
    }



    /**
     * Retrieves the user ID and vehicle associated with a specific vehicle VIN.
     */
    public function getVehicleAndUserIdByVin(string $userId, string $vin): ?array
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting vehicle and user ID by VIN', [
                'userId' => $userId,
                'vin' => $vin,
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                return null;
            }

            $vehicle = $userData->findVehicleByVin($vin);
            if (!$vehicle) {
                return null;
            }

            // Get additional user data
            // Note: ODM UserData doesn't have f2mc field, so we'll set it to null for backward compatibility
            $f2mc = null;
            $userDbId = $userData->getUserId(); // Use userId as userDbId for ODM compatibility

            return [
                'userId' => $userId,
                'vehicle' => $vehicle,
                'f2mc' => $f2mc,
                'userDbId' => $userDbId
            ];
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' An exception occurred while fetching vehicle and user ID by VIN', [
                'userId' => $userId,
                'vin' => $vin,
                'exception' => $e->getMessage()
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                throw new \RuntimeException('MongoDB connection unavailable', 503, $e);
            }

            throw $e;
        }
    }

    /**
     * Updates the feature code array for a specific user's vehicle by VIN.
     */
    public function updateFeatureCodes(string $userId, string $vin, array $featureCodes): bool
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updating feature codes for vehicle', [
                'userId' => $userId,
                'vin' => $vin,
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' User not found', ['userId' => $userId]);
                return false;
            }

            $vehicle = $userData->findVehicleByVin($vin);
            if (!$vehicle) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Vehicle not found', [
                    'userId' => $userId,
                    'vin' => $vin
                ]);
                return false;
            }

            // Update feature codes
            $vehicle->setFeatureCode($featureCodes);

            $this->mongoDBService->save($userData);

            return true;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while saving feature codes for vehicle by vin', [
                'userId' => $userId,
                'vin' => $vin,
                'featureCode' => $featureCodes,
                'exception' => $e->getMessage()
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                throw new \RuntimeException('MongoDB connection unavailable', 503, $e);
            }

            throw $e;
        }
    }

    /**
     * Retrieves the user DB ID and vehicle associated with a specific user ID and VIN.
     */
    public function getVehicleAndUserDBIdByUserIdAndVin(string $userId, string $vin): ?array
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting vehicle and user DB ID by userId and VIN', [
                'userId' => $userId,
                'vin' => $vin,
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                return null;
            }

            $vehicle = $userData->findVehicleByVin($vin);
            if (!$vehicle) {
                return null;
            }

            $userDbId = $userData->getUserId(); // Use userId as userDbId for ODM compatibility

            return ['userDbId' => $userDbId, 'vehicle' => $vehicle];
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' An exception occurred while fetching details by userId & VIN', [
                'userId' => $userId,
                'vin' => $vin,
                'exception' => $e->getMessage()
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                throw new \RuntimeException('MongoDB connection unavailable', 503, $e);
            }

            throw $e;
        }
    }



    /**
     * Get vehicle by criteria
     */
    public function getVehicle(string $userId, string $criteriaValue, string $criteriaKey = 'vin'): ?array
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting vehicle by criteria', [
                'userId' => $userId,
                'criteriaKey' => $criteriaKey,
                'criteriaValue' => $criteriaValue,
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                return null;
            }

            $vehicle = null;
            if ($criteriaKey === 'vin') {
                $vehicle = $userData->findVehicleByVin($criteriaValue);
            } else {
                // For other criteria, search through vehicles
                foreach ($userData->getVehicles() as $v) {
                    $value = match($criteriaKey) {
                        'brand' => $v->getBrand(),
                        'model' => $v->getModel(),
                        'versionId' => $v->getVersionId(),
                        default => null
                    };

                    if ($value === $criteriaValue) {
                        $vehicle = $v;
                        break;
                    }
                }
            }

            if (!$vehicle) {
                return null;
            }

            return [
                'vehicle' => [$vehicle], // Maintain array structure for backward compatibility
                'userId' => $userId,
                'userDbId' => $userData->getUserId(), // Use userId as userDbId for ODM compatibility
            ];
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting vehicle by criteria', [
                'userId' => $userId,
                'criteriaKey' => $criteriaKey,
                'criteriaValue' => $criteriaValue,
                'exception' => $e->getMessage()
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                throw new \RuntimeException('MongoDB connection unavailable', 503, $e);
            }

            return null;
        }
    }

    /**
     * Update vehicle order flag (isOrder) for a specific vehicle
     */
    public function updateVehicleIsOrderFlag(string $userId, string $vehicleId, bool $isOrder): bool
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updating vehicle isOrder flag', [
                'userId' => $userId,
                'vehicleId' => $vehicleId,
                'isOrder' => $isOrder,
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' User not found', [
                    'userId' => $userId,
                    'vehicleId' => $vehicleId,
                ]);
                return false;
            }

            $vehicles = $userData->getVehicles();
            $vehicleFound = false;

            foreach ($vehicles as $vehicle) {
                // Match by VIN (vehicleId is typically VIN)
                if ($vehicle->getVin() === $vehicleId) {
                    $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Vehicle found, updating order status', [
                        'userId' => $userId,
                        'vehicleId' => $vehicleId,
                        'vin' => $vehicle->getVin(),
                        'isOrder' => $isOrder,
                    ]);

                    // For now, we'll use the status field to indicate order status
                    // You may need to add an isOrder field to the Vehicle document
                    if ($isOrder) {
                        $vehicle->setStatus('ORDERED');
                    } else {
                        $vehicle->setStatus('DELIVERED');
                    }

                    $vehicleFound = true;
                    break;
                }
            }

            if (!$vehicleFound) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Vehicle not found', [
                    'userId' => $userId,
                    'vehicleId' => $vehicleId,
                ]);
                return false;
            }

            $this->mongoDBService->save($userData);

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Vehicle order flag updated successfully', [
                'userId' => $userId,
                'vehicleId' => $vehicleId,
                'isOrder' => $isOrder,
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error updating vehicle order flag', [
                'userId' => $userId,
                'vehicleId' => $vehicleId,
                'isOrder' => $isOrder,
                'error' => $e->getMessage(),
                'error_type' => get_class($e),
            ]);

            return false;
        }
    }

    /**
     * Set order updated flag for a specific vehicle
     */
    public function setOrderIsUpdated(string $userId, string $vehicleId, bool $isUpdated): bool
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Setting order updated flag', [
                'userId' => $userId,
                'vehicleId' => $vehicleId,
                'isUpdated' => $isUpdated,
            ]);

            $userData = $this->findUserById($userId);
            if (!$userData) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' User not found', [
                    'userId' => $userId,
                    'vehicleId' => $vehicleId,
                ]);
                return false;
            }

            $vehicles = $userData->getVehicles();
            $vehicleFound = false;

            foreach ($vehicles as $vehicle) {
                // Match by VIN (vehicleId is typically VIN)
                if ($vehicle->getVin() === $vehicleId) {
                    $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Vehicle found, setting updated flag', [
                        'userId' => $userId,
                        'vehicleId' => $vehicleId,
                        'vin' => $vehicle->getVin(),
                        'isUpdated' => $isUpdated,
                    ]);

                    // Note: The Vehicle ODM document doesn't have vehicleOrder.isUpdated field
                    // This functionality might need to be implemented differently
                    // For now, we'll log the action but the field doesn't exist in the ODM model

                    $vehicleFound = true;
                    break;
                }
            }

            if (!$vehicleFound) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Vehicle not found', [
                    'userId' => $userId,
                    'vehicleId' => $vehicleId,
                ]);
                return false;
            }

            // Since the isUpdated field doesn't exist in the ODM model,
            // we'll just log the action for now
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Order updated flag processed (field not in ODM model)', [
                'userId' => $userId,
                'vehicleId' => $vehicleId,
                'isUpdated' => $isUpdated,
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error setting order updated flag', [
                'userId' => $userId,
                'vehicleId' => $vehicleId,
                'isUpdated' => $isUpdated,
                'error' => $e->getMessage(),
                'error_type' => get_class($e),
            ]);

            return false;
        }
    }

    /**
     * Mark multiple orders as read in a single operation (optimized for performance)
     * Since the ODM model doesn't have the 'isUpdated' field, this is a no-op for now
     */
    public function markMultipleOrdersAsRead(string $userId, array $vehicleIds): bool
    {
        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Bulk marking orders as read (ODM optimization)', [
            'userId' => $userId,
            'vehicleCount' => count($vehicleIds),
        ]);

        // Since the ODM Vehicle document doesn't have an 'isUpdated' field,
        // and this functionality was mainly for UI state management,
        // we can safely skip this operation or implement it differently if needed

        $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Orders marked as read (field not in ODM model)', [
            'userId' => $userId,
            'vehicleCount' => count($vehicleIds),
        ]);

        return true;
    }

    /**
     * Get raw vehicle order data from MongoDB document
     * This method queries the raw MongoDB document to check for legacy vehicleOrder data
     */
    public function getRawVehicleOrderData(string $userId, string $vin): ?array
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting raw vehicle order data', [
                'userId' => $userId,
                'vin' => $vin,
            ]);

            // Use the MongoDBService to get raw document data
            $rawUserData = $this->mongoDBService->getRawUserData($userId);
            if (!$rawUserData) {
                $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' No raw user data found', [
                    'userId' => $userId,
                ]);
                return null;
            }

            // Look for vehicles in the raw data
            $vehicles = $rawUserData['vehicle'] ?? [];
            foreach ($vehicles as $vehicleData) {
                if (($vehicleData['vin'] ?? '') === $vin) {
                    // Check if this vehicle has order information
                    $vehicleOrder = $vehicleData['vehicleOrder'] ?? null;
                    if ($vehicleOrder && (!empty($vehicleOrder['mopId']) || !empty($vehicleOrder['orderFormId']))) {
                        $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Found legacy order data in raw MongoDB document', [
                            'vin' => $vin,
                            'userId' => $userId,
                            'mopId' => $vehicleOrder['mopId'] ?? '',
                            'orderFormId' => $vehicleOrder['orderFormId'] ?? '',
                            'trackingStatus' => $vehicleOrder['trackingStatus'] ?? '',
                            'orderFormStatus' => $vehicleOrder['orderFormStatus'] ?? '',
                        ]);

                        return [
                            'mopId' => $vehicleOrder['mopId'] ?? '',
                            'orderFormId' => $vehicleOrder['orderFormId'] ?? '',
                            'trackingStatus' => $vehicleOrder['trackingStatus'] ?? '',
                            'orderFormStatus' => $vehicleOrder['orderFormStatus'] ?? '',
                        ];
                    } else {
                        $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Vehicle found but no order data', [
                            'vin' => $vin,
                            'userId' => $userId,
                            'hasVehicleOrder' => isset($vehicleData['vehicleOrder']),
                            'vehicleOrderData' => $vehicleData['vehicleOrder'] ?? null,
                        ]);
                    }
                }
            }

            $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Vehicle not found in raw data', [
                'userId' => $userId,
                'vin' => $vin,
                'totalVehicles' => count($vehicles),
            ]);

            return null;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting raw vehicle order data', [
                'userId' => $userId,
                'vin' => $vin,
                'error' => $e->getMessage(),
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                throw new \RuntimeException('MongoDB connection unavailable', 503, $e);
            }

            return null;
        }
    }



}