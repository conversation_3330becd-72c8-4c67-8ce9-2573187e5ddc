<?php

declare(strict_types=1);

namespace App\Service;

use App\Connector\ProcessMeConnector;
use App\Helper\WSResponse;

class ProcessMeV2ClientService
{
    public function __construct(
        private ProcessMeConnector $connector
    ) {
    }

    public function addVehicle(
        string $acnt,
        string $vin,
        string $mileage,
        string $brand,
        string $country,
        string $language,
        bool $checkActivation = true,
        ?string $source = null
    ): WSResponse {
        $options = [
            'headers' => [
                'brand' => $brand,
                'country' => $country,
                'language' => $language,
                'checkActivation' => $checkActivation,
            ],
            'json' => [
                'acnt' => $acnt,
                'vin' => $vin,
                'mileage' => $mileage,
            ],
        ];

        if (null !== $source) {
            $options['headers']['source'] = $source;
        }

        return $this->connector->call('POST', '/v2/user_data/vehicles', $options);
    }
}
