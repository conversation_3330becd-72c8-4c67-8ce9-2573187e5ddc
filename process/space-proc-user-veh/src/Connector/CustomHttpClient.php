<?php

namespace App\Connector;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Symfony\Component\HttpFoundation\Response;

/**
 * Kind of HttpClientInterface decorator.
 */
class CustomHttpClient
{
    use LoggerTrait;

    public function __construct(
        private InternalHttpClientService $client
    ) {
        $this->client = $client;
    }

    public function request(string $method, string $url, array $options = []): WSResponse
    {
        try {
            // Log request details with environment information
            $this->logger->info("REQUEST: {$method} {$url}", [
                'method' => $method,
                'url' => $url,
                'options' => json_encode($options),
                'environment' => $_ENV['APP_ENV'] ?? 'unknown',
                'timestamp' => date('Y-m-d H:i:s'),
            ]);
            
            $response = $this->client->request($method, $url, $options);
            $statusCode = $response->getStatusCode();
            $data = Response::HTTP_NO_CONTENT != $statusCode ? $response->toArray(false) : [];
            
            // Log response details
            $this->logger->info("RESPONSE: {$method} {$url}", [
                'status_code' => $statusCode,
                'data' => json_encode($data)
            ]);
            
            $response = new WSResponse($statusCode, $data);

            return $response;
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ': Cached Exception '.$e->getMessage(), [
                'method' => $method,
                'url' => $url,
                'error_code' => $e->getCode(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
            // return new WSResponse($e->getCode(), $e->getMessage());
        }
    }
}
