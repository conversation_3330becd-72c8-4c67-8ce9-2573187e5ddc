<?php

require_once 'vendor/autoload.php';

use Symfony\Component\Dotenv\Dotenv;

// Load environment variables
$dotenv = new Dotenv();
$dotenv->load('.env');

// Bootstrap the Symfony kernel
$kernel = new App\Kernel('dev', true);
$kernel->boot();
$container = $kernel->getContainer();

// Test the UserDataService ODM implementation
try {
    echo "Testing UserDataService ODM Implementation...\n";

    // Get the UserDataService
    $userDataService = $container->get('App\Service\UserDataService');
    echo "✓ UserDataService loaded successfully\n";

    // Test with a sample user ID
    $userId = '100000000f0b4dce874859657ae00001';
    echo "Testing findUserById() with userId: $userId\n";

    // Call the findUserById method
    $userData = $userDataService->findUserById($userId);

    if ($userData) {
        echo "✓ User found in MongoDB ODM\n";
        echo "User ID: " . $userData->getUserId() . "\n";
        echo "Number of vehicles: " . count($userData->getVehicles()) . "\n";

        // Test vehicle retrieval
        $vehicles = $userData->getVehicles();
        if (!empty($vehicles)) {
            echo "✓ Vehicles found:\n";
            foreach ($vehicles as $index => $vehicle) {
                echo "  Vehicle " . ($index + 1) . ":\n";
                echo "    VIN: " . ($vehicle->getVin() ?? 'N/A') . "\n";
                echo "    Brand: " . ($vehicle->getBrand() ?? 'N/A') . "\n";
                echo "    Model: " . ($vehicle->getModel() ?? 'N/A') . "\n";
                echo "    Status: " . ($vehicle->getStatus() ?? 'N/A') . "\n";
            }
        } else {
            echo "  No vehicles found for this user\n";
        }

        echo "✅ SUCCESS: MongoDB ODM implementation is working!\n";
        echo "✅ Pure Doctrine ODM - no MongoDB Atlas API calls\n";
    } else {
        echo "⚠️  User not found (this is normal if no test data exists)\n";
        echo "✅ SUCCESS: ODM query executed without errors\n";
    }

} catch (\Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\nTest completed.\n";
