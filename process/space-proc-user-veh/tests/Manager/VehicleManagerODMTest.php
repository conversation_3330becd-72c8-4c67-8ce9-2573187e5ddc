<?php

namespace App\Tests\Manager;

use App\Manager\VehicleManager;
use App\Service\UserDataService;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Document\Vehicle;
use Psr\Log\LoggerInterface;

class VehicleManagerODMTest extends TestCase
{
    private VehicleManager $vehicleManager;
    private MockObject|UserDataService $userDataService;
    private MockObject|LoggerInterface $logger;

    protected function setUp(): void
    {
        $this->userDataService = $this->createMock(UserDataService::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        // Since VehicleManager has many dependencies and we only want to test the ODM integration,
        // we'll use reflection to create a partial mock that only tests the specific methods
        $this->vehicleManager = $this->getMockBuilder(VehicleManager::class)
            ->disableOriginalConstructor()
            ->onlyMethods([]) // Don't mock any methods, we want to test the real implementation
            ->getMock();

        // Use reflection to inject the dependencies we need
        $reflection = new \ReflectionClass($this->vehicleManager);

        $userDataServiceProperty = $reflection->getProperty('userDataService');
        $userDataServiceProperty->setAccessible(true);
        $userDataServiceProperty->setValue($this->vehicleManager, $this->userDataService);

        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->vehicleManager, $this->logger);
    }

    public function testGetPsaIdUsesUserDataService(): void
    {
        $userId = 'test-user-123';
        $brand = 'PEUGEOT';
        $expectedPsaId = 'PSA123456';

        $this->userDataService
            ->expects($this->once())
            ->method('getPsaIdByUserAndBrand')
            ->with($userId, $brand)
            ->willReturn($expectedPsaId);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->vehicleManager);
        $method = $reflection->getMethod('getPsaId');
        $method->setAccessible(true);

        $result = $method->invoke($this->vehicleManager, $userId, $brand);

        // The method applies RefreshVehicleHelper::parsePsaId, so we expect the parsed result
        $this->assertIsString($result);
    }

    public function testGetUserDbIdUsesUserDataService(): void
    {
        $userId = 'test-user-123';
        $expectedUserDbId = 'userdb123';

        $userData = new UserData();
        $userData->setUserId($userId);
        $userData->setUserDbId($expectedUserDbId);

        $this->userDataService
            ->expects($this->once())
            ->method('findUserById')
            ->with($userId)
            ->willReturn($userData);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->vehicleManager);
        $method = $reflection->getMethod('getUserDbId');
        $method->setAccessible(true);

        $result = $method->invoke($this->vehicleManager, $userId);

        $this->assertEquals($expectedUserDbId, $result);
    }

    public function testGetUserDbIdReturnsNullWhenUserNotFound(): void
    {
        $userId = 'non-existent-user';

        $this->userDataService
            ->expects($this->once())
            ->method('findUserById')
            ->with($userId)
            ->willReturn(null);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->vehicleManager);
        $method = $reflection->getMethod('getUserDbId');
        $method->setAccessible(true);

        $result = $method->invoke($this->vehicleManager, $userId);

        $this->assertNull($result);
    }

    public function testGetVehicleUsesUserDataService(): void
    {
        $userId = 'test-user-123';
        $vin = 'TEST123456789';
        $userDbId = 'userdb123';

        $vehicle = new Vehicle();
        $vehicle->setVin($vin);
        $vehicle->setBrand('PEUGEOT');
        $vehicle->setStatus('SSDP');

        $userData = new UserData();
        $userData->setUserId($userId);
        $userData->setUserDbId($userDbId);
        $userData->addVehicle($vehicle);

        $this->userDataService
            ->expects($this->once())
            ->method('findUserById')
            ->with($userId)
            ->willReturn($userData);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->vehicleManager);
        $method = $reflection->getMethod('getVehicle');
        $method->setAccessible(true);

        $result = $method->invoke($this->vehicleManager, $userId, $vin, 'vin');

        $this->assertIsArray($result);
        $this->assertArrayHasKey('vehicle', $result);
        $this->assertArrayHasKey('userDbId', $result);
        $this->assertEquals($userDbId, $result['userDbId']);
        $this->assertCount(1, $result['vehicle']);
        $this->assertEquals($vehicle, $result['vehicle'][0]);
    }

    public function testGetVehicleReturnsNullWhenUserNotFound(): void
    {
        $userId = 'non-existent-user';
        $vin = 'TEST123456789';

        $this->userDataService
            ->expects($this->once())
            ->method('findUserById')
            ->with($userId)
            ->willReturn(null);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->vehicleManager);
        $method = $reflection->getMethod('getVehicle');
        $method->setAccessible(true);

        $result = $method->invoke($this->vehicleManager, $userId, $vin, 'vin');

        $this->assertNull($result);
    }

    public function testGetVehicleReturnsNullWhenVehicleNotFound(): void
    {
        $userId = 'test-user-123';
        $vin = 'NONEXISTENT123';

        $vehicle = new Vehicle();
        $vehicle->setVin('DIFFERENT123');
        $vehicle->setBrand('PEUGEOT');
        $vehicle->setStatus('SSDP');

        $userData = new UserData();
        $userData->setUserId($userId);
        $userData->addVehicle($vehicle);

        $this->userDataService
            ->expects($this->once())
            ->method('findUserById')
            ->with($userId)
            ->willReturn($userData);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->vehicleManager);
        $method = $reflection->getMethod('getVehicle');
        $method->setAccessible(true);

        $result = $method->invoke($this->vehicleManager, $userId, $vin, 'vin');

        $this->assertNull($result);
    }

    public function testGetVehicleFiltersByStatus(): void
    {
        $userId = 'test-user-123';
        $vin = 'TEST123456789';

        $vehicle = new Vehicle();
        $vehicle->setVin($vin);
        $vehicle->setBrand('PEUGEOT');
        $vehicle->setStatus('GSPD'); // Not SSDP

        $userData = new UserData();
        $userData->setUserId($userId);
        $userData->addVehicle($vehicle);

        $this->userDataService
            ->expects($this->once())
            ->method('findUserById')
            ->with($userId)
            ->willReturn($userData);

        // Use reflection to call the private method
        $reflection = new \ReflectionClass($this->vehicleManager);
        $method = $reflection->getMethod('getVehicle');
        $method->setAccessible(true);

        $result = $method->invoke($this->vehicleManager, $userId, $vin, 'vin');

        // Should return null because vehicle status is not SSDP
        $this->assertNull($result);
    }
}
