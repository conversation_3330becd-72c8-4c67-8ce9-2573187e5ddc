<?php

namespace App\Service;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Document\Vehicle;
use Space\MongoDocuments\Service\MongoDBService;
use Symfony\Component\HttpFoundation\Response;

/**
 * UserDataService handles user data operations using MongoDB ODM
 * Following the established pattern from space-proc-shop microservice
 */
class UserDataService
{
    use LoggerTrait;

    public function __construct(
        private MongoDBService $mongoDBService
    ) {
    }

    /**
     * Find user by ID
     */
    public function findUserById(string $userId): ?UserData
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Finding user by ID', [
                'userId' => $userId,
            ]);

            return $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error finding user by ID', [
                'userId' => $userId,
                'error' => $e->getMessage(),
                'error_type' => get_class($e),
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                throw new \RuntimeException('MongoDB connection unavailable', 503, $e);
            }

            return null;
        }
    }

    private function isMongoConnectionError(\Exception $e): bool
    {
        $message = $e->getMessage();
        return strpos($message, 'TLS handshake failed') !== false ||
               strpos($message, 'No suitable servers found') !== false ||
               strpos($message, 'No servers yet eligible for rescan') !== false ||
               strpos($message, 'serverSelectionTryOnce') !== false;
    }

    /**
     * Save user data
     */
    public function saveUserData(UserData $userData): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Saving user data', [
                'userId' => $userData->getUserId(),
            ]);

            $this->mongoDBService->save($userData);

            return new SuccessResponse('User data saved successfully', Response::HTTP_OK);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error saving user data', [
                'userId' => $userData->getUserId(),
                'error' => $e->getMessage(),
                'error_type' => get_class($e),
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                return new ErrorResponse('Database service temporarily unavailable', Response::HTTP_SERVICE_UNAVAILABLE);
            }

            return new ErrorResponse('Cannot save user data', Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Update user profile data
     */
    public function updateUserProfile(string $userId, array $profileData): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updating user profile', [
                'userId' => $userId,
                'profileData' => $profileData,
            ]);

            // Find user data
            $userData = $this->findUserById($userId);
            if (!$userData) {
                return new ErrorResponse('User not found', Response::HTTP_NOT_FOUND);
            }

            // Get current profile data using reflection or array access
            $profile = [];
            try {
                // Try method access first (if UserData has getProfile method)
                if (method_exists($userData, 'getProfile')) {
                    $profile = $userData->getProfile() ?? [];
                } elseif (method_exists($userData, 'toArray')) {
                    // Try toArray method
                    $userArray = $userData->toArray();
                    $profile = $userArray['profile'] ?? [];
                } else {
                    // Initialize empty profile if none exists
                    $profile = [];
                }
            } catch (\Exception $e) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Could not get existing profile, starting with empty profile', [
                    'error' => $e->getMessage()
                ]);
                $profile = [];
            }

            // Update profile fields
            foreach ($profileData as $key => $value) {
                // Remove 'profile.' prefix if present
                $profileKey = str_replace('profile.', '', $key);
                $profile[$profileKey] = $value;
            }

            // Set updated profile using reflection or available methods
            try {
                if (method_exists($userData, 'setProfile')) {
                    $userData->setProfile($profile);
                } else {
                    // Try to use dynamic property setting if available
                    $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' UserData does not have setProfile method, trying alternative approach');
                    
                    // Create a new UserData with updated profile
                    // For now, we'll use the existing userData and try to save it
                    // The specific implementation depends on the UserData document structure
                    if (property_exists($userData, 'profile')) {
                        $userData->profile = $profile;
                    }
                }
            } catch (\Exception $e) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error setting profile on UserData', [
                    'error' => $e->getMessage()
                ]);
                return new ErrorResponse('Cannot update user profile structure', Response::HTTP_BAD_REQUEST);
            }

            // Save the document
            return $this->saveUserData($userData);
        } catch (\RuntimeException $e) {
            if ($e->getCode() === 503) {
                return new ErrorResponse('Database service temporarily unavailable', Response::HTTP_SERVICE_UNAVAILABLE);
            }
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error updating user profile', [
                'userId' => $userId,
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse('Cannot update user profile', Response::HTTP_BAD_REQUEST);
        }
    }


}
