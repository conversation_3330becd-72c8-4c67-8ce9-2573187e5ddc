<?php

namespace App\Service;

use Symfony\Component\HttpFoundation\Response;

use App\Connector\SysAPDVConnector;
use App\Connector\SysServiceAdvisorConnector;
use App\Helper\BrandHelper;
use App\Helper\WSResponse;
use App\Helper\MarketHelper;
use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\UserData;

class FavoriteDealerService
{
    use LoggerTrait;

    public function __construct(
        private SysAPDVConnector $sysAPDVConnector,
        private SysServiceAdvisorConnector $sysServiceAdvisorConnector,
        private UserDataService $userDataService
    ) {
    }

    public function getXpDealerDetails(array $params)
    {
        $query = [
            'brand' => $params['brand'],
            'country' => $params['country'],
            'language' => $params['language'],
        ];
        if (empty($params['siteGeo'])) {
            return new WSResponse(Response::HTTP_NOT_FOUND, ['error' => 'No favorite dealer found']);
        }
        $response = $this->sysAPDVConnector->call('GET', '/v1/dealer/'.$params['siteGeo'], ['query' => $query]);
        return $response;
    }

    public function getXfDealerDetails(array $params)
    {
        $marketCode = MarketHelper::getMarket($params['country']);
        $brandCode = BrandHelper::getBrandId($params['brand']);
        $dealerIds = explode('|', $params['siteGeo']);
        $query = [
            'brand' => $brandCode,
            'market' => $marketCode,
            'siteCode' => $dealerIds[0] ?? "",
            'sincom' => $dealerIds[1] ?? "",
        ];
        $response = $this->sysServiceAdvisorConnector->call('GET', '/v1/dealer/detail', ['query' => $query]);
        return $response;
    }



    /**
     * Set preferred dealer for brand (XP)
     */
    public function setPreferredDealerForBrand(string $userId, string $brand, array $dealerData): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Setting preferred dealer for brand', [
                'userId' => $userId,
                'brand' => $brand,
                'dealerData' => $dealerData,
            ]);

            // Find or create user data
            $userData = $this->userDataService->findUserById($userId);
            if (!$userData) {
                $userData = new UserData();
                $userData->setUserId($userId);
            }

            // Set preferred dealer for the brand
            $userData->setPreferredDealerForBrand($brand, $dealerData);

            // Save the document using UserDataService
            return $this->userDataService->saveUserData($userData);
        } catch (\RuntimeException $e) {
            if ($e->getCode() === 503) {
                return new ErrorResponse('Database service temporarily unavailable', Response::HTTP_SERVICE_UNAVAILABLE);
            }
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error setting preferred dealer for brand', [
                'userId' => $userId,
                'brand' => $brand,
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse('Cannot save preferred dealer', Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Remove preferred dealer for brand (XP)
     */
    public function removePreferredDealerForBrand(string $userId, string $brand): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Removing preferred dealer for brand', [
                'userId' => $userId,
                'brand' => $brand,
            ]);

            $userData = $this->userDataService->findUserById($userId);
            if (!$userData) {
                return new ErrorResponse('User not found', Response::HTTP_NOT_FOUND);
            }

            // Remove preferred dealer for the brand
            $userData->removePreferredDealerForBrand($brand);

            // Save the document using UserDataService
            return $this->userDataService->saveUserData($userData);
        } catch (\RuntimeException $e) {
            if ($e->getCode() === 503) {
                return new ErrorResponse('Database service temporarily unavailable', Response::HTTP_SERVICE_UNAVAILABLE);
            }
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error removing preferred dealer for brand', [
                'userId' => $userId,
                'brand' => $brand,
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse('Cannot remove preferred dealer', Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Get preferred dealer for brand (XP)
     */
    public function getPreferredDealerForBrand(string $userId, string $brand): ?array
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting preferred dealer for brand', [
                'userId' => $userId,
                'brand' => $brand,
            ]);

            $userData = $this->userDataService->findUserById($userId);
            if (!$userData) {
                return null;
            }

            return $userData->getPreferredDealerForBrand($brand);
        } catch (\RuntimeException $e) {
            if ($e->getCode() === 503) {
                // For read operations, we can't return a ResponseArrayFormat, so we throw the exception
                // to be handled by the calling manager
                throw $e;
            }
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting preferred dealer for brand', [
                'userId' => $userId,
                'brand' => $brand,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Set preferred dealer for vehicle (XF)
     */
    public function setPreferredDealerForVehicle(string $userId, string $vin, array $dealerData): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Setting preferred dealer for vehicle', [
                'userId' => $userId,
                'vin' => $vin,
                'dealerData' => $dealerData,
            ]);

            $userData = $this->userDataService->findUserById($userId);
            if (!$userData) {
                return new ErrorResponse('User not found', Response::HTTP_NOT_FOUND);
            }

            // Find the vehicle by VIN
            $vehicle = $userData->findVehicleByVin($vin);
            if (!$vehicle) {
                return new ErrorResponse('Vehicle not found', Response::HTTP_NOT_FOUND);
            }

            // Set preferred dealer for the vehicle
            $vehicle->setPreferredDealer($dealerData);

            // Save the document using UserDataService
            return $this->userDataService->saveUserData($userData);
        } catch (\RuntimeException $e) {
            if ($e->getCode() === 503) {
                return new ErrorResponse('Database service temporarily unavailable', Response::HTTP_SERVICE_UNAVAILABLE);
            }
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error setting preferred dealer for vehicle', [
                'userId' => $userId,
                'vin' => $vin,
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse('Cannot save preferred dealer', Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Remove preferred dealer for vehicle (XF)
     */
    public function removePreferredDealerForVehicle(string $userId, string $vin): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Removing preferred dealer for vehicle', [
                'userId' => $userId,
                'vin' => $vin,
            ]);

            $userData = $this->userDataService->findUserById($userId);
            if (!$userData) {
                return new ErrorResponse('User not found', Response::HTTP_NOT_FOUND);
            }

            // Find the vehicle by VIN
            $vehicle = $userData->findVehicleByVin($vin);
            if (!$vehicle) {
                return new ErrorResponse('Vehicle not found', Response::HTTP_NOT_FOUND);
            }

            // Remove preferred dealer from the vehicle
            $vehicle->setPreferredDealer(null);

            // Save the document using UserDataService
            return $this->userDataService->saveUserData($userData);
        } catch (\RuntimeException $e) {
            if ($e->getCode() === 503) {
                return new ErrorResponse('Database service temporarily unavailable', Response::HTTP_SERVICE_UNAVAILABLE);
            }
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error removing preferred dealer for vehicle', [
                'userId' => $userId,
                'vin' => $vin,
                'error' => $e->getMessage(),
            ]);
            return new ErrorResponse('Cannot remove preferred dealer', Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * Get preferred dealer for vehicle (XF)
     */
    public function getPreferredDealerForVehicle(string $userId, string $vin): ?array
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting preferred dealer for vehicle', [
                'userId' => $userId,
                'vin' => $vin,
            ]);

            $userData = $this->userDataService->findUserById($userId);
            if (!$userData) {
                return null;
            }

            // Find the vehicle by VIN
            $vehicle = $userData->findVehicleByVin($vin);
            if (!$vehicle) {
                return null;
            }

            return $vehicle->getPreferredDealer();
        } catch (\RuntimeException $e) {
            if ($e->getCode() === 503) {
                // For read operations, we can't return a ResponseArrayFormat, so we throw the exception
                // to be handled by the calling manager
                throw $e;
            }
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting preferred dealer for vehicle', [
                'userId' => $userId,
                'vin' => $vin,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }
}