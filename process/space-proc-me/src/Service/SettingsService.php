<?php

namespace App\Service;

use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\Settings;
use Space\MongoDocuments\Repository\SettingsRepository;

/**
 * SettingsService handles settings operations using MongoDB ODM
 * Following the established pattern from space-proc-shop microservice
 */
class SettingsService
{
    use LoggerTrait;

    public function __construct(
        private SettingsRepository $settingsRepository
    ) {
    }

    /**
     * Get O2X settings for a brand and source
     */
    public function getO2xSettings(string $brand, string $source = "APP"): array
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting O2X settings', [
                'brand' => $brand,
                'source' => $source,
            ]);

            $filter = [
                'brand' => $brand,
                'source' => $source,
                'culture' => '',
                '$or' => [
                    ["settingsData.o2x.code" => "o2x"],
                    ["settingsData.config.code" => "o2x"]
                ]
            ];

            // Use SettingsRepository to find settings
            $settings = $this->settingsRepository->findByComplexFilter($filter);
            if (!$settings) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' No settings found for o2x', [
                    'brand' => $brand,
                    'source' => $source,
                ]);
                return [];
            }

            $settingsDatas = $settings->getSettingsData();
            if ('APP' == $source) {
                $data = current(array_filter($settingsDatas, function ($item) {
                    $code = $item['config']['code'] ?? '';
                    return $code == 'o2x';
                }));
                if ($data) {
                    $config = $data['config'] ?? [];
                    $result = [...$data, ...$config];
                    unset($result['config']);
                } else {
                    $result = $settingsDatas['o2x'] ?? [];
                }

                return $result ?? [];
            }
            return $settingsDatas['o2x'] ?? [];
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error while getting o2x settings', [
                'brand' => $brand,
                'source' => $source,
                'exception_code' => $e->getCode(),
                'exception_message' => $e->getMessage(),
                'error_type' => get_class($e),
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' MongoDB connection unavailable, returning empty settings');
            }

            return [];
        }
    }

    /**
     * Find settings by custom filter
     */
    public function findSettingsByFilter(array $filter): ?Settings
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Finding settings by filter', [
                'filter' => $filter,
            ]);

            return $this->settingsRepository->findByComplexFilter($filter);
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error finding settings by filter', [
                'filter' => $filter,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    private function isMongoConnectionError(\Exception $e): bool
    {
        $message = $e->getMessage();
        return strpos($message, 'TLS handshake failed') !== false ||
               strpos($message, 'No suitable servers found') !== false ||
               strpos($message, 'No servers yet eligible for rescan') !== false ||
               strpos($message, 'serverSelectionTryOnce') !== false;
    }
}
