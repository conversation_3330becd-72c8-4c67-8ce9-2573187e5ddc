<?php

namespace Space\MongoDocuments\Service;

use Doctrine\ODM\MongoDB\DocumentManager;
use Psr\Log\LoggerInterface;

class MongoDBService
{
    public function __construct(
        private DocumentManager $documentManager,
        private LoggerInterface $logger
    ) {
    }

    /**
     * Find a document by ID
     */
    public function find(string $documentClass, string $id)
    {
        try {
            return $this->documentManager->find($documentClass, $id);
        } catch (\Exception $e) {
            $this->logger->error('Error finding document', [
                'class' => $documentClass,
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Find documents by criteria
     */
    public function findBy(string $documentClass, array $criteria, array $orderBy = null, $limit = null, $offset = null)
    {
        $this->logger->info('MongoDBService: Starting findBy query', [
            'document_class' => $documentClass,
            'criteria' => $criteria,
            'orderBy' => $orderBy,
            'limit' => $limit,
            'offset' => $offset
        ]);

        try {
            $result = $this->documentManager->getRepository($documentClass)->findBy($criteria, $orderBy, $limit, $offset);

            $this->logger->info('MongoDBService: findBy query completed', [
                'document_class' => $documentClass,
                'criteria' => $criteria,
                'result_count' => count($result)
            ]);

            return $result;
        } catch (\Exception $e) {
            $this->logger->error('MongoDBService: Error finding documents by criteria', [
                'class' => $documentClass,
                'criteria' => $criteria,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Find a single document by criteria
     */
    public function findOneBy(string $documentClass, array $criteria)
    {
        $this->logger->info('MongoDBService: Starting findOneBy query', [
            'document_class' => $documentClass,
            'criteria' => $criteria,
            'database' => $this->documentManager->getConfiguration()->getDefaultDB()
        ]);

        try {
            $repository = $this->documentManager->getRepository($documentClass);

            $this->logger->info('MongoDBService: Repository details', [
                'repository_class' => get_class($repository),
                'document_class' => $documentClass
            ]);

            // Log connection details
            $connection = $this->documentManager->getClient();
            $this->logger->info('MongoDBService: Connection details', [
                'connection_class' => get_class($connection),
                'database_name' => $this->documentManager->getConfiguration()->getDefaultDB()
            ]);

            $result = $repository->findOneBy($criteria);

            if ($result) {
                $this->logger->info('MongoDBService: Document found', [
                    'document_class' => $documentClass,
                    'criteria' => $criteria,
                    'result_id' => method_exists($result, 'getId') ? $result->getId() : 'no_id_method',
                    'result_class' => get_class($result)
                ]);
            } else {
                $this->logger->warning('MongoDBService: No document found', [
                    'document_class' => $documentClass,
                    'criteria' => $criteria
                ]);

                // Try to get collection stats for debugging
                try {
                    $collection = $this->documentManager->getDocumentCollection($documentClass);
                    $collectionName = $collection->getCollectionName();
                    $this->logger->info('MongoDBService: Collection info', [
                        'collection_name' => $collectionName,
                        'document_class' => $documentClass
                    ]);
                } catch (\Exception $collectionError) {
                    $this->logger->error('MongoDBService: Error getting collection info', [
                        'error' => $collectionError->getMessage()
                    ]);
                }
            }

            return $result;
        } catch (\Exception $e) {
            $this->logger->error('MongoDBService: Error finding document by criteria', [
                'class' => $documentClass,
                'criteria' => $criteria,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return null;
        }
    }

    /**
     * Save a document
     */
    public function save($document, bool $flush = true): void
    {
        try {
            $this->documentManager->persist($document);

            if ($flush) {
                $this->documentManager->flush();
            }
        } catch (\Exception $e) {
            $this->logger->error('Error saving document', [
                'document' => get_class($document),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Update a document
     */
    public function update($document): void
    {
        try {
            $this->documentManager->flush();
        } catch (\Exception $e) {
            $this->logger->error('Error updating document', [
                'document' => get_class($document),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Remove a document
     */
    public function remove($document, bool $flush = true): void
    {
        try {
            $this->documentManager->remove($document);

            if ($flush) {
                $this->documentManager->flush();
            }
        } catch (\Exception $e) {
            $this->logger->error('Error removing document', [
                'document' => get_class($document),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }



    /**
     * Get the document manager
     */
    public function getDocumentManager(): DocumentManager
    {
        return $this->documentManager;
    }

    /**
     * Get raw user data from MongoDB collection
     * This method returns the raw document data as an array, useful for accessing legacy fields
     */
    public function getRawUserData(string $userId): ?array
    {
        try {
            $this->logger->info('MongoDBService: Getting raw user data', [
                'userId' => $userId
            ]);

            // Get the collection directly
            $collection = $this->documentManager->getDocumentCollection('Space\MongoDocuments\Document\UserData');

            // Find the document using MongoDB driver directly
            $document = $collection->findOne(['userId' => $userId]);

            if ($document) {
                // Convert MongoDB document to array
                // Check if document is already an array or needs conversion
                if (is_array($document)) {
                    $rawData = $document;
                } else {
                    $rawData = $document->toArray();
                }

                $this->logger->info('MongoDBService: Raw user data found', [
                    'userId' => $userId,
                    'hasVehicles' => isset($rawData['vehicle']),
                    'vehicleCount' => isset($rawData['vehicle']) ? count($rawData['vehicle']) : 0
                ]);

                return $rawData;
            } else {
                $this->logger->warning('MongoDBService: No raw user data found', [
                    'userId' => $userId
                ]);
                return null;
            }
        } catch (\Exception $e) {
            $this->logger->error('MongoDBService: Error getting raw user data', [
                'userId' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }
}
