<?php

namespace Space\MongoDocuments\Document;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;
use Space\MongoDocuments\Repository\UserDataRepository;

#[MongoDB\Document(collection: 'userData', repositoryClass: UserDataRepository::class)]
class UserData
{
    #[MongoDB\Id]
    #[Serializer\SerializedName("_id")]
    private ?string $id = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $userId = null;

    #[MongoDB\EmbedMany(targetDocument: Vehicle::class, name: 'vehicle')]
    private Collection $vehicle;

    #[MongoDB\EmbedMany(targetDocument: UserPsaId::class)]
    private Collection $userPsaId;

    #[MongoDB\Field(type: 'hash')]
    private ?array $preferredDealer = null;

    public function __construct()
    {
        $this->vehicle = new ArrayCollection();
        $this->userPsaId = new ArrayCollection();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getUserId(): ?string
    {
        return $this->userId;
    }

    public function setUserId(string $userId): self
    {
        $this->userId = $userId;
        return $this;
    }

    /**
     * @return Vehicle[]
     */
    public function getVehicles(): array
    {
        return $this->vehicle->toArray();
    }

    /**
     * @param Vehicle[] $vehicles
     */
    public function setVehicles(array $vehicles): self
    {
        $this->vehicle->clear();
        foreach ($vehicles as $vehicle) {
            $this->vehicle->add($vehicle);
        }
        return $this;
    }

    public function addVehicle(Vehicle $vehicle): self
    {
        $this->vehicle->add($vehicle);
        return $this;
    }

    /**
     * @return UserPsaId[]|null
     */
    public function getUserPsaId(): ?array
    {
        return $this->userPsaId->isEmpty() ? null : $this->userPsaId->toArray();
    }

    /**
     * @param UserPsaId[]|null $userPsaId
     */
    public function setUserPsaId(?array $userPsaId): self
    {
        $this->userPsaId->clear();
        if ($userPsaId !== null) {
            foreach ($userPsaId as $psaId) {
                $this->userPsaId->add($psaId);
            }
        }
        return $this;
    }

    // Preferred dealer methods
    public function getPreferredDealer(): ?array
    {
        return $this->preferredDealer;
    }

    public function setPreferredDealer(?array $preferredDealer): self
    {
        $this->preferredDealer = $preferredDealer;
        return $this;
    }

    /**
     * Get preferred dealer for a specific brand
     */
    public function getPreferredDealerForBrand(string $brand): ?array
    {
        return $this->preferredDealer[strtolower($brand)] ?? null;
    }

    /**
     * Set preferred dealer for a specific brand
     */
    public function setPreferredDealerForBrand(string $brand, array $dealerData): self
    {
        if ($this->preferredDealer === null) {
            $this->preferredDealer = [];
        }
        $this->preferredDealer[strtolower($brand)] = $dealerData;
        return $this;
    }

    /**
     * Remove preferred dealer for a specific brand
     */
    public function removePreferredDealerForBrand(string $brand): self
    {
        if ($this->preferredDealer !== null) {
            unset($this->preferredDealer[strtolower($brand)]);
            if (empty($this->preferredDealer)) {
                $this->preferredDealer = null;
            }
        }
        return $this;
    }

    /**
     * Find a vehicle by VIN
     */
    public function findVehicleByVin(string $vin): ?Vehicle
    {
        foreach ($this->vehicle as $vehicle) {
            if ($vehicle->getVin() === $vin) {
                return $vehicle;
            }
        }
        return null;
    }
}
